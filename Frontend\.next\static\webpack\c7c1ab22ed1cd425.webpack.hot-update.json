{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CBootstrapClient%5CBootstrapClient.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCircularButtonWithArrow%5CCircularButtonWithArrow.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CCookiesConsentBanner%5CCookiesConsentBanner.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CFooter%5CFooter.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeader%5CHeader.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbase.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cbootstrap%5Cdist%5Ccss%5Cbootstrap.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cscript.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-bailout-to-csr.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22100%22%2C%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-poppins%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&server=false!"]}