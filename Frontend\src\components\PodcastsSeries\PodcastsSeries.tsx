'use client';

import { useState } from 'react';
import styles from './PodcastsSeries.module.css';
import { Container } from 'react-bootstrap';
import Heading from '@components/Heading';
import classNames from '@utils/classNames';
import Image from 'next/image';
import Link from 'next/link';
import YTVideo from '@components/YTVideo';

export const PodcastTabs = ({
  PodcastsSeriesData,
  CloudPagePodcastData,
  playButtonUrl,
}: {
  PodcastsSeriesData?;
  CloudPagePodcastData?;
  playButtonUrl?;
}) => {
  const [activeEpisodes, setActiveEpisodes] = useState<Record<number, number>>(
    () => {
      return PodcastsSeriesData?.reduce(
        (acc, series) => {
          acc[series?.id] = series?.podcast_episode[0]?.id || 0;
          return acc;
        },
        {} as Record<number, number>,
      );
    },
  );

  const handleEpisodeClick = (seriesId: number, episodeId: number) => {
    setActiveEpisodes(prev => ({
      ...prev,
      [seriesId]: episodeId,
    }));
  };

  return (
    <>
      {PodcastsSeriesData ? (
        <Container fluid className={styles.podcast_series_container}>
          {PodcastsSeriesData?.map(series => (
            <div
              key={series?.id}
              className={classNames(
                styles.podcast_series,
                series?.variant === 'black' && styles.podcast_variant_black,
              )}
            >
              <Heading
                title={series?.podcast_series_title}
                headingType="h2"
                className={styles.podcast_series_title}
              />

              <div className={styles.episode_tabs}>
                {series?.podcast_episode?.map(episode => (
                  <button
                    key={episode?.id}
                    className={classNames(
                      series?.variant === 'black'
                        ? styles.tab_button_varient_black
                        : styles.tab_button_varient_white,
                      series?.variant === 'black' &&
                        activeEpisodes[series?.id] === episode?.id &&
                        styles.tab_button_active_varient_black,
                      series?.variant === 'white' &&
                        activeEpisodes[series?.id] === episode?.id &&
                        styles.tab_button_active_varient_white,
                    )}
                    onClick={() => handleEpisodeClick(series?.id, episode?.id)}
                  >
                    {episode?.episode_listing}
                  </button>
                ))}
              </div>

              {series?.podcast_episode?.map(episode => (
                <div
                  key={episode?.id}
                  className={classNames(
                    styles.episode_content,
                    activeEpisodes[series?.id] === episode?.id &&
                      styles.episode_content_active,
                  )}
                >
                  <div
                    className={classNames(
                      styles.content_wrapper,
                      series?.layout === 'Content: Left & Video: Right' &&
                        styles.flex_row_reverse,
                    )}
                  >
                    <div className={styles.video_section}>
                      {episode?.video_thumbnail_image?.data?.attributes?.url &&
                        episode?.youtube_video_embed_link && (
                          <YTVideo
                            embedLink={episode?.youtube_video_embed_link}
                            playButtonUrl={playButtonUrl}
                            thumbnailUrl={
                              episode?.video_thumbnail_image?.data?.attributes
                                ?.url
                            }
                          />
                        )}
                    </div>
                    <div className={styles.content_section}>
                      <Heading
                        title={episode?.title}
                        headingType="h3"
                        className={styles.episode_title}
                      />
                      <div
                        className={styles.episode_description}
                        dangerouslySetInnerHTML={{
                          __html: episode?.description,
                        }}
                      />
                      <div className={styles.audio_section}>
                        {episode?.podcast_audio_file?.data?.attributes?.url && (
                          <audio
                            className={styles.audio_player}
                            src={
                              episode?.podcast_audio_file?.data?.attributes?.url
                            }
                            controls
                            controlsList="nodownload"
                          />
                        )}
                        {episode?.spotify_audio_full_url && (
                          <Link
                            href={episode?.spotify_audio_full_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            prefetch={false}
                          >
                            <Image
                              src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/image_116_489fbe2b10.svg`}
                              alt="Listen on Spotify"
                              width={36}
                              height={36}
                            />
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ))}
        </Container>
      ) : (
        <Container fluid className={styles.podcast_series_container}>
          <div
            className={classNames(
              styles.podcast_series,
              CloudPagePodcastData?.variant === 'black' &&
                styles.podcast_variant_black,
            )}
          >
            <Heading
              title={CloudPagePodcastData?.podcast_series_title}
              headingType="h2"
              className={styles.podcast_series_title}
            />

            {CloudPagePodcastData?.podcast_episode[0] && (
              <div className={styles.episode_content_active}>
                <div
                  className={classNames(
                    styles.content_wrapper,
                    CloudPagePodcastData?.layout ===
                      'Content: Left & Video: Right' && styles.flex_row_reverse,
                  )}
                >
                  <div className={styles.video_section}>
                    {CloudPagePodcastData?.podcast_episode[0]
                      ?.video_thumbnail_image?.data?.attributes?.url &&
                      CloudPagePodcastData?.podcast_episode[0]
                        ?.youtube_video_embed_link && (
                        <YTVideo
                          embedLink={
                            CloudPagePodcastData?.podcast_episode[0]
                              ?.youtube_video_embed_link
                          }
                          playButtonUrl={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}//Play_Button_bd09bb5741.svg`}
                          thumbnailUrl={
                            CloudPagePodcastData?.podcast_episode[0]
                              ?.video_thumbnail_image?.data?.attributes?.url
                          }
                        />
                      )}
                  </div>
                  <div className={styles.content_section}>
                    <Heading
                      title={CloudPagePodcastData?.podcast_episode[0]?.title}
                      headingType="h3"
                      className={styles.episode_title}
                    />
                    <div
                      className={styles.episode_description}
                      dangerouslySetInnerHTML={{
                        __html:
                          CloudPagePodcastData?.podcast_episode[0]?.description,
                      }}
                    />
                    <div className={styles.audio_section}>
                      {CloudPagePodcastData?.podcast_episode[0]
                        ?.podcast_audio_file?.data?.attributes?.url && (
                        <audio
                          className={styles.audio_player}
                          src={
                            CloudPagePodcastData?.podcast_episode[0]
                              ?.podcast_audio_file?.data?.attributes?.url
                          }
                          controls
                          controlsList="nodownload"
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Container>
      )}
    </>
  );
};

export default PodcastTabs;
