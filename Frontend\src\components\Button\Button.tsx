'use client';

import { ReactNode, SyntheticEvent } from 'react';
import Link from 'next/link';
import classNames from '@utils/classNames';

import styles from './Button.module.css';

interface ButtonProps {
  label?: string;
  className?: string;
  type?: 'submit' | 'reset' | 'button';
  isLink?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  leftIcon?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  rightIcon?: any;
  href?: string;
  isExternal?: boolean;
  onClick?: (event: SyntheticEvent<Element, Event>) => void;
  dataID?: string;
  onMouseDown?: (event: SyntheticEvent<Element, Event>) => void;
  onMouseUp?: (event: SyntheticEvent<Element, Event>) => void;
  onTouchStart?: (event: SyntheticEvent<Element, Event>) => void;
  onTouchEnd?: (event: SyntheticEvent<Element, Event>) => void;
  children?: ReactNode;
  scrollToForm?: any;
}

export default function Button({
  label = '',
  className = '',
  type = 'button',
  isLink = false,
  leftIcon = null,
  rightIcon = null,
  href = '',
  children = null,
  isExternal = false,
  onClick = () => {},
  dataID = null,
  onMouseDown = () => {},
  onMouseUp = () => {},
  onTouchStart = () => {},
  onTouchEnd = () => {},
  scrollToForm,
}: ButtonProps) {
  const inner = (
    <div className={styles.innerWrapper}>
      {leftIcon && <span className={styles.leftWrapper}>{leftIcon}</span>}
      {label}
      {rightIcon && <span className={styles.rightWrapper}>{rightIcon}</span>}
    </div>
  );
  const target = isExternal ? '_blank' : '_self';
  const rel = isExternal ? 'noreferrer' : null;

  const handleButtonClick = e => {
    if (scrollToForm) {
      scrollToForm();
    }
    if (onClick) {
      onClick(e);
    }
  };

  return !isLink ? (
    <button
      // eslint-disable-next-line react/button-has-type
      type={type}
      className={classNames(styles.button, className)}
      data-id={dataID}
      onClick={e => handleButtonClick(e)}
      onMouseDown={onMouseDown}
      onMouseUp={onMouseUp}
      onTouchStart={onTouchStart}
      onTouchEnd={onTouchEnd}
    >
      {inner}
      {children}
    </button>
  ) : (
    <Link
      href={href}
      target={target}
      rel={rel}
      className={classNames(styles.link, className)}
      data-id={dataID}
      onClick={onClick}
      prefetch={false}
    >
      <div>{inner}</div>
    </Link>
  );
}
